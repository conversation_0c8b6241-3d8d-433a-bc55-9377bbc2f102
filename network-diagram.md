flowchart TB
    subgraph Client_Layer[Client Layer]
        C[Client (App/Web)]
    end

    subgraph Edge_Layer[Edge Layer]
        I[Internet]
        CF[CloudFront (CDN)]
    end

    subgraph VPC[VPC]
        subgraph DMZ[DMZ - Public Subnet]
            LB[Load Balancer]
        end

        subgraph Public_Subnet[Public Subnet]
            NAT[NAT Gateway]
        end

        subgraph Private_Subnet[Private Subnet]
            API1[API Server 1]
            API2[API Server 2]
            DB[(Database - RDS)]
        end

        subgraph Storage[Storage]
            S3[(S3 Bucket)]
        end
    end

    subgraph External_Services[External Services]
        Third[Third-party Service (Payment, SMS...)]
        FCM[FCM (Push Notification)]
    end

    %% Connections
    C --> I
    I --> LB
    I --> CF
    CF --> S3

    LB --> API1
    LB --> API2
    API1 --> DB
    API2 --> DB

    API1 --> NAT
    API2 --> NAT
    NAT --> Third

    API1 --> FCM
    API2 --> FCM
